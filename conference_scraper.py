#!/usr/bin/env python3
"""
Conference File Scraper for XP 2025
Scrapes conference files and organizes them in the specified folder structure:
<Date> -> <Time Block> -> <Conference Name> -> <File>
"""

import requests
from bs4 import BeautifulSoup
import os
import re
import json
from urllib.parse import urljoin, urlparse, parse_qs
import time
from pathlib import Path
import logging
from excel_tracker import ConferenceExcelTracker

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConferenceScraper:
    def __init__(self, base_url="https://conf.researchr.org", excel_file="conference_tracking.xlsx"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.excel_tracker = ConferenceExcelTracker(excel_file)
        
    def get_page_content(self, url):
        """Fetch page content with error handling"""
        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            logger.error(f"Error fetching {url}: {e}")
            return None
    
    def find_file_attached_links(self, html_content):
        """Find all links that contain 'File Attached'"""
        soup = BeautifulSoup(html_content, 'html.parser')
        file_links = []
        
        # Find all <a> tags that contain "File Attached"
        for link in soup.find_all('a', string=lambda text: text and 'File Attached' in text):
            href = link.get('href')
            if href:
                file_links.append(href)
                
        logger.info(f"Found {len(file_links)} links with 'File Attached'")
        return file_links
    
    def extract_session_info(self, link_element):
        """Extract session information from the HTML structure around the link"""
        # Navigate up to find the table row containing session info
        tr = link_element.find_parent('tr')
        if not tr:
            return None

        # Find the table containing this row to get date info
        table = tr.find_parent('table')
        if not table:
            return None

        # Extract date from table data attribute
        date = table.get('data-facet-date', '')
        room = table.get('data-facet-room', '')

        # Find the session info in the table
        session_info = {}

        # Look for time slot - try multiple approaches
        time_div = tr.find('div', class_='slot-label')
        if time_div:
            session_info['time_block'] = time_div.get_text(strip=True)
        else:
            # Look for start-time in the row
            start_time_div = tr.find('div', class_='start-time')
            if start_time_div:
                session_info['time_block'] = start_time_div.get_text(strip=True)
            else:
                # Look in the table header for time slot
                header_rows = table.find_all('tr', class_='session-details')
                for header_row in header_rows:
                    slot_div = header_row.find('div', class_='slot-label')
                    if slot_div:
                        session_info['time_block'] = slot_div.get_text(strip=True)
                        break

        # Extract session title
        title_link = tr.find('a', {'data-event-modal': True})
        if title_link:
            session_info['title'] = title_link.get_text(strip=True)

        # Extract track info
        track_div = tr.find('div', class_='prog-track')
        if track_div:
            session_info['track'] = track_div.get_text(strip=True)

        # Extract session block info from table header
        session_block_info = self.extract_session_block_info(table)
        session_info.update(session_block_info)

        session_info['date'] = date
        session_info['room'] = room

        return session_info

    def extract_session_block_info(self, table):
        """Extract session block information (session name, tracks, room)"""
        session_block = {}

        # Look for session header row
        header_row = table.find('tr', class_='session-details')
        if header_row:
            # Extract session name and info
            session_info_div = header_row.find('div', class_='session-info-in-table')
            if session_info_div:
                # Get the first text node (session name) before any links
                session_name = ""
                for content in session_info_div.contents:
                    if hasattr(content, 'strip'):  # It's a text node
                        session_name = content.strip()
                        break

                # If no direct text, try to extract from the full text
                if not session_name:
                    full_text = session_info_div.get_text(strip=True)
                    # Split by common separators to get the session name
                    for separator in [' at ', 'at ', ' - ', ' | ']:
                        if separator in full_text:
                            session_name = full_text.split(separator)[0].strip()
                            break
                    else:
                        session_name = full_text.strip()

                session_block['session_name'] = session_name

                # Extract room link if available
                room_link = session_info_div.find('a', class_='room-link')
                if room_link:
                    room_text = room_link.get_text(strip=True)
                    session_block['session_room'] = room_text

        return session_block
    
    def parse_download_onclick(self, onclick_attr):
        """Parse the onclick attribute to extract download parameters"""
        if not onclick_attr:
            return None

        logger.debug(f"Parsing onclick: {onclick_attr}")

        # Extract parameters from the onclick JavaScript
        # Pattern: serverInvokeDownloadCompatible("url","action", [params], "", this, "1")
        # The onclick might have quotes escaped differently

        # Try different patterns for different quote escaping
        patterns = [
            r'serverInvokeDownloadCompatible\("([^"]+)","([^"]+)",\s*\[([^\]]+)\]',
            r'serverInvokeDownloadCompatible\(&quot;([^&]+)&quot;,&quot;([^&]+)&quot;,\s*\[([^\]]+)\]',
            r"serverInvokeDownloadCompatible\('([^']+)','([^']+)',\s*\[([^\]]+)\]"
        ]

        match = None
        for pattern in patterns:
            match = re.search(pattern, onclick_attr)
            if match:
                break

        if not match:
            logger.error(f"No pattern matched for onclick: {onclick_attr[:200]}...")
            return None

        base_url = match.group(1)
        action = match.group(2)
        params_str = match.group(3)

        logger.debug(f"Extracted: base_url={base_url}, action={action}, params_str={params_str}")

        # Parse parameters - try different quote patterns
        params = {}
        param_patterns = [
            r'\{"name":"([^"]+)","value":"([^"]+)"\}',
            r'\{&quot;name&quot;:&quot;([^&]+)&quot;,&quot;value&quot;:&quot;([^&]+)&quot;\}',
            r"\\{'name':'([^']+)','value':'([^']+)'\\}",
            r'"name":"([^"]+)","value":"([^"]+)"'  # Simple pattern without braces
        ]

        for param_pattern in param_patterns:
            matches = list(re.finditer(param_pattern, params_str))
            if matches:
                logger.debug(f"Pattern {param_pattern} found {len(matches)} matches")
                for param_match in matches:
                    params[param_match.group(1)] = param_match.group(2)
                break

        # If no pattern worked, try a more flexible approach
        if not params:
            logger.debug("Trying flexible parameter parsing...")
            # Look for name-value pairs in any format
            name_matches = re.findall(r'"name"\s*:\s*"([^"]+)"', params_str)
            value_matches = re.findall(r'"value"\s*:\s*"([^"]+)"', params_str)

            if len(name_matches) == len(value_matches):
                for name, value in zip(name_matches, value_matches):
                    params[name] = value

        logger.debug(f"Parsed parameters: {params}")

        # Construct download URL
        download_url = f"{base_url}?action-call-with-get-request-type=1&{action}=1&__ajax_runtime_request__=1"
        for key, value in params.items():
            download_url += f"&{key}={value}"

        return download_url
    
    def get_download_info_from_detail_page(self, detail_url):
        """Extract download information from a detail page"""
        html_content = self.get_page_content(detail_url)
        if not html_content:
            logger.error(f"Failed to get content from {detail_url}")
            return []

        soup = BeautifulSoup(html_content, 'html.parser')
        downloads = []

        # Look for file attachments table
        file_table = soup.find('table', class_='table table-bordered table-striped table-condensed')
        if not file_table:
            logger.warning(f"No file attachments table found on {detail_url}")
            # Debug: Look for any tables
            all_tables = soup.find_all('table')
            logger.info(f"Found {len(all_tables)} tables on the page")
            return downloads

        logger.info(f"Found file attachments table on {detail_url}")

        # Find download links in the table
        for row in file_table.find_all('tr'):
            download_link = row.find('a', class_='downloadlink')
            if download_link:
                onclick = download_link.get('onclick', '')
                filename = download_link.get_text(strip=True)

                logger.info(f"Found download link: {filename}")
                logger.debug(f"Onclick attribute: {onclick[:100]}...")

                download_url = self.parse_download_onclick(onclick)
                if download_url:
                    downloads.append({
                        'filename': filename,
                        'download_url': download_url
                    })
                    logger.info(f"Parsed download URL: {download_url[:100]}...")
                else:
                    logger.error(f"Failed to parse download URL from onclick: {onclick[:100]}...")

        logger.info(f"Found {len(downloads)} downloadable files on {detail_url}")
        return downloads

    def sanitize_filename(self, filename):
        """Sanitize filename for filesystem compatibility"""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename.strip()

    def create_folder_structure(self, session_info, base_dir="downloads"):
        """Create folder structure: Date -> Time Block -> Session Block Name"""
        if not session_info:
            return base_dir

        # Clean up the date format
        date = session_info.get('date', 'Unknown_Date')
        date = self.sanitize_filename(date)

        # Clean up time block
        time_block = session_info.get('time_block', 'Unknown_Time')
        time_block = self.sanitize_filename(time_block.replace(':', '_').replace(' ', '_'))

        # Create session block name
        session_name = session_info.get('session_name', '')
        session_room = session_info.get('session_room', '')
        track = session_info.get('track', '')

        # Build session block folder name
        if session_name:
            # Use session name as the main identifier
            session_block_name = session_name

            # Add track info if available and different from session name
            if track and track not in session_name:
                session_block_name += f" / {track}"

            # Add room info if available
            if session_room:
                session_block_name += f" at {session_room}"
        else:
            # Fallback to individual presentation if no session block found
            title = session_info.get('title', 'Unknown_Session')
            if track:
                session_block_name = f"{title} ({track})"
            else:
                session_block_name = title

        session_block_name = self.sanitize_filename(session_block_name)

        # Create full path
        folder_path = os.path.join(base_dir, date, time_block, session_block_name)

        # Create directories
        Path(folder_path).mkdir(parents=True, exist_ok=True)

        return folder_path

    def download_file(self, download_url, filename, folder_path):
        """Download a file to the specified folder and return download info"""
        download_info = {
            'filename': filename,
            'download_url': download_url,
            'folder_path': folder_path,
            'file_path': '',
            'file_size_mb': 0,
            'success': False,
            'download_time': '',
            'error': ''
        }

        try:
            start_time = time.time()
            response = self.session.get(download_url, stream=True)
            response.raise_for_status()

            file_path = os.path.join(folder_path, self.sanitize_filename(filename))
            download_info['file_path'] = os.path.relpath(file_path)

            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            # Get file size
            file_size = os.path.getsize(file_path)
            download_info['file_size_mb'] = file_size / (1024 * 1024)
            download_info['success'] = True
            download_info['download_time'] = f"{time.time() - start_time:.2f}s"

            logger.info(f"Downloaded: {file_path}")
            return download_info

        except Exception as e:
            error_msg = str(e)
            download_info['error'] = error_msg
            logger.error(f"Error downloading {filename}: {error_msg}")
            return download_info

    def find_all_session_links(self, soup):
        """Find all session links, both with and without files"""
        all_session_links = []

        # Find all session links by looking for data-event-modal attributes
        session_links = soup.find_all('a', {'data-event-modal': True})

        for link in session_links:
            # Get the href which should be the detail page
            href = link.get('href')
            if href and href.startswith('#'):
                # This is a modal link, we need to construct the detail URL
                # Extract the event ID and build the detail URL
                event_id = link.get('data-event-modal')
                if event_id:
                    # Find the corresponding detail link in the same row
                    tr = link.find_parent('tr')
                    if tr:
                        # Look for publication links in the same row
                        pub_links = tr.find_all('a', class_='publication-link')
                        detail_link = None

                        for pub_link in pub_links:
                            pub_href = pub_link.get('href')
                            if pub_href and 'details' in pub_href:
                                detail_link = pub_href
                                break

                        if detail_link:
                            all_session_links.append({
                                'link_element': link,
                                'detail_url': detail_link,
                                'has_files': 'File Attached' in tr.get_text()
                            })
                        else:
                            # No detail link found, create a placeholder
                            all_session_links.append({
                                'link_element': link,
                                'detail_url': f"#event-{event_id}",
                                'has_files': False
                            })

        return all_session_links

    def group_sessions_by_blocks(self, all_sessions):
        """Group sessions by session blocks (same time slot, date, and session name)"""
        session_blocks = {}

        for session_data in all_sessions:
            try:
                link = session_data['link_element']
                session_info = self.extract_session_info(link)

                if not session_info:
                    continue

                # Create a key for grouping sessions
                date = session_info.get('date', 'Unknown_Date')
                time_block = session_info.get('time_block', 'Unknown_Time')
                session_name = session_info.get('session_name', '')
                session_room = session_info.get('session_room', '')

                # If no session name, use individual presentation grouping
                if not session_name:
                    # Use individual presentation as its own "block"
                    title = session_info.get('title', 'Unknown_Session')
                    track = session_info.get('track', '')
                    block_key = f"{date}|{time_block}|{title}|{track}"
                else:
                    # Group by session block (same session name and room, regardless of individual time)
                    # This groups all presentations in the same session together
                    block_key = f"{date}|{session_name}|{session_room}"

                if block_key not in session_blocks:
                    session_blocks[block_key] = []

                session_blocks[block_key].append({
                    'session_data': session_data,
                    'session_info': session_info
                })

            except Exception as e:
                logger.error(f"Error grouping session: {e}")
                continue

        # Sort sessions within each block by time
        for block_key in session_blocks:
            session_blocks[block_key].sort(key=lambda x: x['session_info'].get('time_block', ''))

        return session_blocks

    def calculate_session_time_range(self, sessions_in_block):
        """Calculate the time range for a session block"""
        times = []

        for session in sessions_in_block:
            time_block = session['session_info'].get('time_block', '')
            if time_block:
                times.append(time_block)

        if not times:
            return 'Unknown_Time'

        # Sort times and get first and last
        times.sort()

        if len(times) == 1:
            return times[0]

        # Extract start time from first and end time from last
        first_time = times[0]
        last_time = times[-1]

        # Try to extract time ranges
        def extract_time_range(time_str):
            # Handle formats like "09:00 - 12:30" or "09:00"
            if ' - ' in time_str:
                start, end = time_str.split(' - ')
                return start.strip(), end.strip()
            else:
                return time_str.strip(), None

        first_start, first_end = extract_time_range(first_time)
        last_start, last_end = extract_time_range(last_time)

        # Create combined time range
        start_time = first_start
        end_time = last_end if last_end else last_start

        if start_time == end_time:
            return start_time
        else:
            return f"{start_time}-{end_time}"

    def scrape_conference_files(self, program_url):
        """Main method to scrape all conference sessions and files"""
        logger.info(f"Starting to scrape: {program_url}")

        # Get the main program page
        html_content = self.get_page_content(program_url)
        if not html_content:
            logger.error("Failed to fetch program page")
            return

        soup = BeautifulSoup(html_content, 'html.parser')

        # Find ALL session links (with and without files)
        all_sessions = self.find_all_session_links(soup)

        logger.info(f"Found {len(all_sessions)} total sessions")

        sessions_with_files = sum(1 for session in all_sessions if session['has_files'])
        sessions_without_files = len(all_sessions) - sessions_with_files

        logger.info(f"  - {sessions_with_files} sessions with files")
        logger.info(f"  - {sessions_without_files} sessions without files")

        # Group sessions by session blocks
        session_blocks = self.group_sessions_by_blocks(all_sessions)

        logger.info(f"Grouped into {len(session_blocks)} session blocks")

        total_downloads = 0

        for block_key, sessions_in_block in session_blocks.items():
            try:
                logger.info(f"Processing session block: {block_key}")

                # Use the first session to determine folder structure, but calculate time range
                first_session = sessions_in_block[0]
                link = first_session['session_data']['link_element']
                session_info = self.extract_session_info(link)

                # Calculate time range for the session block
                time_range = self.calculate_session_time_range(sessions_in_block)
                session_info['time_block'] = time_range

                # Create folder structure for this session block
                folder_path = self.create_folder_structure(session_info)

                # Process all sessions in this block
                all_download_results = []

                for session in sessions_in_block:
                    session_data = session['session_data']
                    link = session_data['link_element']
                    detail_url = session_data['detail_url']
                    has_files = session_data['has_files']

                    # Extract session information
                    session_info = self.extract_session_info(link)

                    # Convert relative URL to absolute if needed
                    if detail_url.startswith('/'):
                        detail_url = urljoin(self.base_url, detail_url)
                    elif detail_url.startswith('#'):
                        detail_url = f"{program_url}{detail_url}"

                    logger.info(f"  - {session_info.get('title', 'Unknown')} - Files: {has_files}")

                    download_results = []

                    if has_files and not detail_url.startswith('#'):
                        # Get download information from detail page
                        downloads = self.get_download_info_from_detail_page(detail_url)

                        if downloads:
                            # Download each file to the shared session block folder
                            for download_info in downloads:
                                download_result = self.download_file(
                                    download_info['download_url'],
                                    download_info['filename'],
                                    folder_path
                                )
                                download_results.append(download_result)

                                if download_result['success']:
                                    total_downloads += 1

                    # Add session to Excel tracker
                    self.excel_tracker.add_session(session_info, detail_url, download_results)
                    all_download_results.extend(download_results)

                    # Add delay to be respectful to the server
                    time.sleep(0.3)

                logger.info(f"  Session block completed: {len(all_download_results)} files downloaded")

            except Exception as e:
                logger.error(f"Error processing session block {block_key}: {e}")
                # Still add sessions to tracker even if there was an error
                for session in sessions_in_block:
                    try:
                        link = session['session_data'].get('link_element')
                        if link:
                            session_info = self.extract_session_info(link) or {}
                            detail_url = session['session_data'].get('detail_url', '')
                            self.excel_tracker.add_session(session_info, detail_url, [])
                    except:
                        pass
                continue

        logger.info(f"Scraping completed. Total files downloaded: {total_downloads}")

        # Generate Excel report
        logger.info("Generating Excel tracking report...")
        if self.excel_tracker.generate_excel_report():
            logger.info(f"Excel report saved: {self.excel_tracker.output_file}")
            logger.info(f"Tracked {self.excel_tracker.get_session_count()} sessions and {self.excel_tracker.get_file_count()} files")
        else:
            logger.error("Failed to generate Excel report")

def main():
    """Main function to run the scraper"""
    scraper = ConferenceScraper(excel_file="conference_tracking_full.xlsx")
    program_url = "https://conf.researchr.org/program/xp-2025/program-xp-2025/"

    scraper.scrape_conference_files(program_url)

if __name__ == "__main__":
    main()
