#!/usr/bin/env python3
"""
Summary script to display the contents of the Excel tracking file
"""

import pandas as pd
import os

def display_excel_summary(excel_file="conference_tracking.xlsx"):
    """Display a summary of the Excel tracking file"""
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        return
    
    print(f"📊 XP 2025 Conference Tracking Summary")
    print(f"📁 File: {excel_file}")
    print(f"📏 Size: {os.path.getsize(excel_file) / 1024:.2f} KB")
    print("=" * 60)
    
    try:
        # Read all sheets
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        print(f"📋 Available sheets: {list(excel_data.keys())}")
        print()
        
        # Sessions Overview
        if 'Sessions_Overview' in excel_data:
            sessions_df = excel_data['Sessions_Overview']
            print("🎯 SESSIONS OVERVIEW")
            print("-" * 30)
            print(f"Total sessions: {len(sessions_df)}")
            print(f"Sessions with files: {sessions_df['Has_Files'].sum()}")
            print(f"Sessions without files: {len(sessions_df) - sessions_df['Has_Files'].sum()}")
            print(f"Total files: {sessions_df['File_Count'].sum()}")
            print()
            
            # Show sample sessions
            print("📝 Sample sessions:")
            for i, row in sessions_df.head(3).iterrows():
                print(f"  {i+1}. {row['Session_Title'][:50]}...")
                print(f"     📅 {row['Date']} ⏰ {row['Time_Block']} 🏷️ {row['Track']}")
                print(f"     📁 Files: {row['File_Count']} | Status: {row['Download_Status']}")
                if pd.notna(row['Folder_Path']) and row['Folder_Path']:
                    print(f"     📂 Path: {row['Folder_Path'][:60]}...")
                print()
        
        # Files Detail
        if 'Files_Detail' in excel_data:
            files_df = excel_data['Files_Detail']
            print("📄 FILES DETAIL")
            print("-" * 30)
            print(f"Total files tracked: {len(files_df)}")
            print(f"Successfully downloaded: {files_df['Download_Success'].sum()}")
            print(f"Failed downloads: {len(files_df) - files_df['Download_Success'].sum()}")
            print(f"Total size: {files_df['File_Size_MB'].sum():.2f} MB")
            print(f"Average file size: {files_df['File_Size_MB'].mean():.2f} MB")
            print()
            
            # Show file types
            file_extensions = files_df['Filename'].str.extract(r'\.([^.]+)$')[0].value_counts()
            print("📊 File types:")
            for ext, count in file_extensions.head(5).items():
                print(f"  .{ext}: {count} files")
            print()
            
            # Show sample files
            print("📝 Sample files:")
            for i, row in files_df.head(3).iterrows():
                status = "✅" if row['Download_Success'] else "❌"
                print(f"  {status} {row['Filename']}")
                print(f"     📅 {row['Date']} | 📏 {row['File_Size_MB']:.2f} MB")
                if pd.notna(row['File_Path']) and row['File_Path']:
                    print(f"     📂 {row['File_Path'][:60]}...")
                print()
        
        # Summary Statistics
        if 'Summary' in excel_data:
            print("📈 SUMMARY STATISTICS")
            print("-" * 30)
            summary_df = excel_data['Summary']
            for i, row in summary_df.iterrows():
                if pd.notna(row.iloc[0]) and pd.notna(row.iloc[1]):
                    print(f"  {row.iloc[0]}: {row.iloc[1]}")
            print()
        
        print("✅ Excel file successfully processed!")
        
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")

if __name__ == "__main__":
    display_excel_summary()
