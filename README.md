# XP 2025 Conference File Scraper & Excel Tracker

A comprehensive Python-based scraper for the XP 2025 conference that downloads all attached files and generates detailed Excel tracking reports.

## 🎯 Features

- **Automated File Scraping**: Downloads all conference files from sessions with attachments
- **Organized Folder Structure**: Creates folders by Date → Time Block → Session Name
- **Excel Tracking**: Generates comprehensive Excel reports with multiple sheets
- **Session Metadata**: Tracks all session details, file information, and download status
- **Error Handling**: Robust error handling with detailed logging
- **Progress Tracking**: Real-time progress updates during scraping

## 📁 Folder Structure

The scraper organizes downloaded files in the following structure:
```
downloads/
├── Mon 2 Jun 2025/
│   ├── 09_15/
│   │   └── Session Title (Track Name)/
│   │       └── filename.pdf
│   ├── 14_00/
│   └── ...
├── Tue 3 Jun 2025/
├── Wed 4 Jun 2025/
└── Thu 5 Jun 2025/
```

## 📊 Excel Reports

The system generates Excel files with three sheets:

### 1. Sessions Overview
- **Date**: Conference date
- **Time_Block**: Session time slot
- **Session_Title**: Full session title
- **Track**: Conference track/category
- **Detail_URL**: Link to session details
- **Has_Files**: Boolean indicating if files are attached
- **File_Count**: Number of attached files
- **Download_Status**: Download status (Downloaded/No Files)
- **Folder_Path**: Relative path to downloaded files
- **Scraped_At**: Timestamp of when data was collected

### 2. Files Detail
- **Session_Title**: Session name
- **Date**: Conference date
- **Time_Block**: Session time
- **Track**: Conference track
- **File_Number**: File number within session
- **Filename**: Original filename
- **Download_URL**: Direct download URL
- **File_Path**: Local file path
- **File_Size_MB**: File size in megabytes
- **Download_Success**: Success/failure status
- **Download_Time**: Time taken to download
- **Error_Message**: Error details if download failed

### 3. Summary
- Overall statistics
- Breakdown by date
- Breakdown by track
- File type analysis

## 🚀 Usage

### Prerequisites
```bash
pip install -r requirements.txt
```

### Full Scraper (Downloads + Excel)
```bash
python conference_scraper.py
```
- Downloads all files
- Creates folder structure
- Generates `conference_tracking_full.xlsx`

### Excel-Only Mode (No Downloads)
```bash
python excel_only_scraper.py
```
- Processes all sessions
- Checks existing files
- Generates `conference_tracking.xlsx`

### View Excel Summary
```bash
python excel_summary.py
```
- Displays Excel file contents
- Shows statistics and sample data

### View Downloaded Files Summary
```bash
python download_summary.py
```
- Shows folder structure
- Displays file counts and sizes

## 📋 Files Description

| File | Purpose |
|------|---------|
| `conference_scraper.py` | Main scraper with download + Excel functionality |
| `excel_tracker.py` | Excel generation module |
| `excel_only_scraper.py` | Excel tracking without downloads |
| `excel_summary.py` | Display Excel file contents |
| `download_summary.py` | Show downloaded files structure |
| `test_excel_tracker.py` | Test Excel functionality |
| `requirements.txt` | Python dependencies |

## 📈 Statistics (Latest Run)

- **Total Sessions**: 128 (ALL conference sessions)
- **Sessions with Files**: 44
- **Sessions without Files**: 84
- **Total Files Downloaded**: 49
- **Total Size**: 196.32 MB
- **Average File Size**: 4.01 MB
- **File Types**: PDF (44), PPTX (5)

### By Date:
- **Mon 2 Jun 2025**: 43 sessions (11 with files, 12 files)
- **Tue 3 Jun 2025**: 29 sessions (16 with files, 18 files)
- **Wed 4 Jun 2025**: 28 sessions (8 with files, 9 files)
- **Thu 5 Jun 2025**: 28 sessions (9 with files, 10 files)

### By Track:
- **Research Papers**: 17 sessions (16 with files, 19 files)
- **Industry and Practice**: 22 sessions (10 with files, 11 files)
- **Catering**: 18 sessions (0 with files)
- **Keynotes**: 11 sessions (0 with files)
- **AIandAgile**: 15 sessions (5 with files, 6 files)
- **AgileEAD**: 10 sessions (3 with files, 3 files)
- **GoHyb**: 9 sessions (2 with files, 2 files)
- **Agile Training and Education**: 8 sessions (3 with files, 3 files)
- **Experience Reports**: 6 sessions (4 with files, 4 files)
- **Open Space**: 5 sessions (0 with files)
- **Industry and Practice Workshops**: 5 sessions (1 with files, 1 file)
- **Posters**: 1 session (0 with files)
- **AgilePR**: 1 session (0 with files)

## 🛠️ Technical Details

### Dependencies
- `requests`: HTTP requests
- `beautifulsoup4`: HTML parsing
- `pandas`: Data manipulation
- `openpyxl`: Excel file generation
- `pathlib`: File system operations

### Key Features
- **JavaScript Download URL Parsing**: Handles complex onclick JavaScript functions
- **Robust Session Info Extraction**: Multiple fallback methods for extracting session metadata
- **File System Safety**: Sanitizes filenames and handles path creation
- **Respectful Scraping**: Includes delays between requests
- **Comprehensive Logging**: Detailed progress and error logging

## 🔧 Customization

### Change Output Locations
```python
# In conference_scraper.py
scraper = ConferenceScraper(excel_file="my_tracking.xlsx")

# In create_folder_structure method
folder_path = os.path.join("my_downloads", date, time_block, conference_name)
```

### Modify Excel Columns
Edit the `excel_tracker.py` file to add/remove columns in the `add_session` method.

### Adjust Scraping Delays
```python
# In scrape_conference_files method
time.sleep(1)  # Change delay between requests
```

## 📝 Notes

- The scraper respects the conference website's structure and includes appropriate delays
- All file paths are sanitized for cross-platform compatibility
- Excel files include auto-adjusted column widths for readability
- The system handles both successful downloads and errors gracefully
- Relative paths are used in Excel files for portability

## 🎉 Success Metrics

✅ **128 sessions processed** (ALL conference sessions)
✅ **44 sessions with files tracked**
✅ **84 sessions without files tracked**
✅ **49 files downloaded**
✅ **196.32 MB of conference materials**
✅ **100% download success rate**
✅ **Comprehensive Excel tracking for ALL sessions**
✅ **Organized folder structure**
✅ **Complete conference coverage**
