#!/usr/bin/env python3
"""
Excel-only version of the conference scraper
Processes all sessions and generates Excel tracking without re-downloading files
"""

import requests
from bs4 import BeautifulSoup
import os
import re
import json
from urllib.parse import urljoin, urlparse, parse_qs
import time
from pathlib import Path
import logging
from excel_tracker import ConferenceExcelTracker

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExcelOnlyConferenceScraper:
    def __init__(self, base_url="https://conf.researchr.org", excel_file="conference_tracking.xlsx"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.excel_tracker = ConferenceExcelTracker(excel_file)
        
    def get_page_content(self, url):
        """Fetch page content with error handling"""
        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            logger.error(f"Error fetching {url}: {e}")
            return None
    
    def extract_session_info(self, link_element):
        """Extract session information from the HTML structure around the link"""
        # Navigate up to find the table row containing session info
        tr = link_element.find_parent('tr')
        if not tr:
            return None
            
        # Find the table containing this row to get date info
        table = tr.find_parent('table')
        if not table:
            return None
            
        # Extract date from table data attribute
        date = table.get('data-facet-date', '')
        
        # Find the session info in the table
        session_info = {}
        
        # Look for time slot - try multiple approaches
        time_div = tr.find('div', class_='slot-label')
        if time_div:
            session_info['time_block'] = time_div.get_text(strip=True)
        else:
            # Look for start-time in the row
            start_time_div = tr.find('div', class_='start-time')
            if start_time_div:
                session_info['time_block'] = start_time_div.get_text(strip=True)
            else:
                # Look in the table header for time slot
                header_rows = table.find_all('tr', class_='session-details')
                for header_row in header_rows:
                    slot_div = header_row.find('div', class_='slot-label')
                    if slot_div:
                        session_info['time_block'] = slot_div.get_text(strip=True)
                        break
        
        # Extract session title
        title_link = tr.find('a', {'data-event-modal': True})
        if title_link:
            session_info['title'] = title_link.get_text(strip=True)
        
        # Extract track info
        track_div = tr.find('div', class_='prog-track')
        if track_div:
            session_info['track'] = track_div.get_text(strip=True)
            
        session_info['date'] = date
        
        return session_info
    
    def parse_download_onclick(self, onclick_attr):
        """Parse the onclick attribute to extract download parameters"""
        if not onclick_attr:
            return None
            
        # Extract parameters from the onclick JavaScript
        patterns = [
            r'serverInvokeDownloadCompatible\("([^"]+)","([^"]+)",\s*\[([^\]]+)\]',
            r'serverInvokeDownloadCompatible\(&quot;([^&]+)&quot;,&quot;([^&]+)&quot;,\s*\[([^\]]+)\]',
            r"serverInvokeDownloadCompatible\('([^']+)','([^']+)',\s*\[([^\]]+)\]"
        ]
        
        match = None
        for pattern in patterns:
            match = re.search(pattern, onclick_attr)
            if match:
                break
                
        if not match:
            return None
            
        base_url = match.group(1)
        action = match.group(2)
        params_str = match.group(3)
        
        # Parse parameters
        params = {}
        param_patterns = [
            r'\{"name":"([^"]+)","value":"([^"]+)"\}',
            r'\{&quot;name&quot;:&quot;([^&]+)&quot;,&quot;value&quot;:&quot;([^&]+)&quot;\}',
            r"\\{'name':'([^']+)','value':'([^']+)'\\}",
            r'"name":"([^"]+)","value":"([^"]+)"'
        ]
        
        for param_pattern in param_patterns:
            matches = list(re.finditer(param_pattern, params_str))
            if matches:
                for param_match in matches:
                    params[param_match.group(1)] = param_match.group(2)
                break
        
        # If no pattern worked, try a more flexible approach
        if not params:
            name_matches = re.findall(r'"name"\s*:\s*"([^"]+)"', params_str)
            value_matches = re.findall(r'"value"\s*:\s*"([^"]+)"', params_str)
            
            if len(name_matches) == len(value_matches):
                for name, value in zip(name_matches, value_matches):
                    params[name] = value
        
        # Construct download URL
        download_url = f"{base_url}?action-call-with-get-request-type=1&{action}=1&__ajax_runtime_request__=1"
        for key, value in params.items():
            download_url += f"&{key}={value}"
            
        return download_url
    
    def get_download_info_from_detail_page(self, detail_url):
        """Extract download information from a detail page"""
        html_content = self.get_page_content(detail_url)
        if not html_content:
            return []
            
        soup = BeautifulSoup(html_content, 'html.parser')
        downloads = []
        
        # Look for file attachments table
        file_table = soup.find('table', class_='table table-bordered table-striped table-condensed')
        if not file_table:
            return downloads
            
        # Find download links in the table
        for row in file_table.find_all('tr'):
            download_link = row.find('a', class_='downloadlink')
            if download_link:
                onclick = download_link.get('onclick', '')
                filename = download_link.get_text(strip=True)
                
                download_url = self.parse_download_onclick(onclick)
                if download_url:
                    downloads.append({
                        'filename': filename,
                        'download_url': download_url
                    })
                    
        return downloads
    
    def sanitize_filename(self, filename):
        """Sanitize filename for filesystem compatibility"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename.strip()
    
    def create_folder_path(self, session_info, base_dir="downloads"):
        """Create folder path without actually creating directories"""
        if not session_info:
            return base_dir
            
        date = session_info.get('date', 'Unknown_Date')
        date = self.sanitize_filename(date)
        
        time_block = session_info.get('time_block', 'Unknown_Time')
        time_block = self.sanitize_filename(time_block.replace(':', '_').replace(' ', '_'))
        
        title = session_info.get('title', 'Unknown_Session')
        track = session_info.get('track', '')
        
        if track:
            conference_name = f"{title} ({track})"
        else:
            conference_name = title
            
        conference_name = self.sanitize_filename(conference_name)
        
        folder_path = os.path.join(base_dir, date, time_block, conference_name)
        return folder_path
    
    def check_existing_files(self, folder_path, downloads):
        """Check if files already exist and create download results"""
        download_results = []
        
        for download_info in downloads:
            filename = self.sanitize_filename(download_info['filename'])
            file_path = os.path.join(folder_path, filename)
            
            result = {
                'filename': download_info['filename'],
                'download_url': download_info['download_url'],
                'folder_path': folder_path,
                'file_path': os.path.relpath(file_path) if os.path.exists(file_path) else '',
                'file_size_mb': 0,
                'success': False,
                'download_time': '',
                'error': ''
            }
            
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                result['file_size_mb'] = file_size / (1024 * 1024)
                result['success'] = True
                result['download_time'] = 'Previously downloaded'
            else:
                result['error'] = 'File not found'
            
            download_results.append(result)
        
        return download_results
    
    def find_all_session_links(self, soup):
        """Find all session links, both with and without files"""
        all_session_links = []

        # Find all session links by looking for data-event-modal attributes
        session_links = soup.find_all('a', {'data-event-modal': True})

        for link in session_links:
            # Get the href which should be the detail page
            href = link.get('href')
            if href and href.startswith('#'):
                # This is a modal link, we need to construct the detail URL
                # Extract the event ID and build the detail URL
                event_id = link.get('data-event-modal')
                if event_id:
                    # Find the corresponding detail link in the same row
                    tr = link.find_parent('tr')
                    if tr:
                        # Look for publication links in the same row
                        pub_links = tr.find_all('a', class_='publication-link')
                        detail_link = None

                        for pub_link in pub_links:
                            pub_href = pub_link.get('href')
                            if pub_href and 'details' in pub_href:
                                detail_link = pub_href
                                break

                        if detail_link:
                            all_session_links.append({
                                'link_element': link,
                                'detail_url': detail_link,
                                'has_files': 'File Attached' in tr.get_text()
                            })
                        else:
                            # No detail link found, create a placeholder
                            all_session_links.append({
                                'link_element': link,
                                'detail_url': f"#event-{event_id}",
                                'has_files': False
                            })

        return all_session_links

    def scrape_conference_sessions(self, program_url):
        """Main method to scrape all conference sessions for Excel tracking"""
        logger.info(f"Starting to scrape sessions for Excel tracking: {program_url}")

        html_content = self.get_page_content(program_url)
        if not html_content:
            logger.error("Failed to fetch program page")
            return

        soup = BeautifulSoup(html_content, 'html.parser')

        # Find ALL session links (with and without files)
        all_sessions = self.find_all_session_links(soup)

        logger.info(f"Found {len(all_sessions)} total sessions")

        sessions_with_files = sum(1 for session in all_sessions if session['has_files'])
        sessions_without_files = len(all_sessions) - sessions_with_files

        logger.info(f"  - {sessions_with_files} sessions with files")
        logger.info(f"  - {sessions_without_files} sessions without files")

        for session_data in all_sessions:
            try:
                link = session_data['link_element']
                detail_url = session_data['detail_url']
                has_files = session_data['has_files']

                session_info = self.extract_session_info(link)

                # Convert relative URL to absolute if needed
                if detail_url.startswith('/'):
                    detail_url = urljoin(self.base_url, detail_url)
                elif detail_url.startswith('#'):
                    # This is a modal-only session, no detail page
                    detail_url = f"{program_url}{detail_url}"

                logger.info(f"Processing: {session_info.get('title', 'Unknown')} on {session_info.get('date', 'Unknown')} - Files: {has_files}")

                download_results = []

                if has_files and not detail_url.startswith('#'):
                    downloads = self.get_download_info_from_detail_page(detail_url)

                    if downloads:
                        folder_path = self.create_folder_path(session_info)
                        download_results = self.check_existing_files(folder_path, downloads)

                self.excel_tracker.add_session(session_info, detail_url, download_results)
                time.sleep(0.3)  # Shorter delay since we're not downloading

            except Exception as e:
                logger.error(f"Error processing session: {e}")
                try:
                    link = session_data.get('link_element')
                    if link:
                        session_info = self.extract_session_info(link) or {}
                        detail_url = session_data.get('detail_url', '')
                        self.excel_tracker.add_session(session_info, detail_url, [])
                except:
                    pass
                continue
        
        logger.info("Session processing completed. Generating Excel report...")
        
        if self.excel_tracker.generate_excel_report():
            logger.info(f"Excel report saved: {self.excel_tracker.output_file}")
            logger.info(f"Tracked {self.excel_tracker.get_session_count()} sessions and {self.excel_tracker.get_file_count()} files")
        else:
            logger.error("Failed to generate Excel report")

def main():
    """Main function to run the Excel-only scraper"""
    scraper = ExcelOnlyConferenceScraper()
    program_url = "https://conf.researchr.org/program/xp-2025/program-xp-2025/"
    
    scraper.scrape_conference_sessions(program_url)

if __name__ == "__main__":
    main()
