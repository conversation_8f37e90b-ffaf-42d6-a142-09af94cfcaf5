#!/usr/bin/env python3
"""
Test script to verify the new session block folder structure
"""

import requests
from bs4 import BeautifulSoup
import os
import re
from urllib.parse import urljoin
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SessionBlockTester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_page_content(self, url):
        """Fetch page content with error handling"""
        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            logger.error(f"Error fetching {url}: {e}")
            return None
    
    def sanitize_filename(self, filename):
        """Sanitize filename for filesystem compatibility"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename.strip()
    
    def extract_session_block_info(self, table):
        """Extract session block information (session name, tracks, room)"""
        session_block = {}

        # Look for session header row
        header_row = table.find('tr', class_='session-details')
        if header_row:
            # Extract session name and info
            session_info_div = header_row.find('div', class_='session-info-in-table')
            if session_info_div:
                # Get the first text node (session name) before any links
                session_name = ""
                for content in session_info_div.contents:
                    if hasattr(content, 'strip'):  # It's a text node
                        session_name = content.strip()
                        break

                # If no direct text, try to extract from the full text
                if not session_name:
                    full_text = session_info_div.get_text(strip=True)
                    # Split by common separators to get the session name
                    for separator in [' at ', 'at ', ' - ', ' | ']:
                        if separator in full_text:
                            session_name = full_text.split(separator)[0].strip()
                            break
                    else:
                        session_name = full_text.strip()

                session_block['session_name'] = session_name

                # Extract room link if available
                room_link = session_info_div.find('a', class_='room-link')
                if room_link:
                    room_text = room_link.get_text(strip=True)
                    session_block['session_room'] = room_text

        return session_block
    
    def extract_session_info(self, link_element):
        """Extract session information from the HTML structure around the link"""
        # Navigate up to find the table row containing session info
        tr = link_element.find_parent('tr')
        if not tr:
            return None
            
        # Find the table containing this row to get date info
        table = tr.find_parent('table')
        if not table:
            return None
            
        # Extract date from table data attribute
        date = table.get('data-facet-date', '')
        room = table.get('data-facet-room', '')
        
        # Find the session info in the table
        session_info = {}
        
        # Look for time slot - try multiple approaches
        time_div = tr.find('div', class_='slot-label')
        if time_div:
            session_info['time_block'] = time_div.get_text(strip=True)
        else:
            # Look for start-time in the row
            start_time_div = tr.find('div', class_='start-time')
            if start_time_div:
                session_info['time_block'] = start_time_div.get_text(strip=True)
            else:
                # Look in the table header for time slot
                header_rows = table.find_all('tr', class_='session-details')
                for header_row in header_rows:
                    slot_div = header_row.find('div', class_='slot-label')
                    if slot_div:
                        session_info['time_block'] = slot_div.get_text(strip=True)
                        break
        
        # Extract session title
        title_link = tr.find('a', {'data-event-modal': True})
        if title_link:
            session_info['title'] = title_link.get_text(strip=True)
        
        # Extract track info
        track_div = tr.find('div', class_='prog-track')
        if track_div:
            session_info['track'] = track_div.get_text(strip=True)
        
        # Extract session block info from table header
        session_block_info = self.extract_session_block_info(table)
        session_info.update(session_block_info)
            
        session_info['date'] = date
        session_info['room'] = room
        
        return session_info
    
    def create_folder_path(self, session_info, base_dir="test_downloads"):
        """Create folder path for session blocks"""
        if not session_info:
            return base_dir
            
        # Clean up the date format
        date = session_info.get('date', 'Unknown_Date')
        date = self.sanitize_filename(date)
        
        # Clean up time block
        time_block = session_info.get('time_block', 'Unknown_Time')
        time_block = self.sanitize_filename(time_block.replace(':', '_').replace(' ', '_'))
        
        # Create session block name
        session_name = session_info.get('session_name', '')
        session_room = session_info.get('session_room', '')
        track = session_info.get('track', '')
        
        # Build session block folder name
        if session_name:
            # Use session name as the main identifier
            session_block_name = session_name
            
            # Add track info if available and different from session name
            if track and track not in session_name:
                session_block_name += f" / {track}"
                
            # Add room info if available
            if session_room:
                session_block_name += f" at {session_room}"
        else:
            # Fallback to individual presentation if no session block found
            title = session_info.get('title', 'Unknown_Session')
            if track:
                session_block_name = f"{title} ({track})"
            else:
                session_block_name = title
                
        session_block_name = self.sanitize_filename(session_block_name)
        
        folder_path = os.path.join(base_dir, date, time_block, session_block_name)
        return folder_path
    
    def test_session_structure(self, program_url):
        """Test the new session block structure"""
        logger.info(f"Testing session block structure from: {program_url}")
        
        html_content = self.get_page_content(program_url)
        if not html_content:
            logger.error("Failed to fetch program page")
            return
            
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Find all session links
        session_links = soup.find_all('a', {'data-event-modal': True})
        
        logger.info(f"Found {len(session_links)} session links")
        
        # Test first 10 sessions
        for i, link in enumerate(session_links[:10]):
            session_info = self.extract_session_info(link)
            if session_info:
                folder_path = self.create_folder_path(session_info)
                
                print(f"\n{i+1}. Session: {session_info.get('title', 'Unknown')[:60]}...")
                print(f"   Date: {session_info.get('date', 'Unknown')}")
                print(f"   Time: {session_info.get('time_block', 'Unknown')}")
                print(f"   Track: {session_info.get('track', 'Unknown')}")
                print(f"   Session Name: {session_info.get('session_name', 'None')}")
                print(f"   Session Room: {session_info.get('session_room', 'None')}")
                print(f"   Folder Path: {folder_path}")

def main():
    """Main function to test session block structure"""
    tester = SessionBlockTester()
    program_url = "https://conf.researchr.org/program/xp-2025/program-xp-2025/"
    
    tester.test_session_structure(program_url)

if __name__ == "__main__":
    main()
