#!/usr/bin/env python3
"""
Script to reorganize existing downloads into the new session block structure
"""

import os
import shutil
from pathlib import Path
import logging
from conference_scraper import ConferenceScraper
from bs4 import BeautifulSoup

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DownloadReorganizer:
    def __init__(self):
        self.scraper = ConferenceScraper()
        self.old_downloads_dir = "downloads"
        self.new_downloads_dir = "downloads_session_blocks"
        
    def reorganize_downloads(self):
        """Reorganize existing downloads into session block structure"""
        logger.info("Starting download reorganization...")
        
        if not os.path.exists(self.old_downloads_dir):
            logger.error(f"Old downloads directory not found: {self.old_downloads_dir}")
            return
        
        # Create new downloads directory
        if os.path.exists(self.new_downloads_dir):
            shutil.rmtree(self.new_downloads_dir)
        os.makedirs(self.new_downloads_dir)
        
        # Get session information from the website
        program_url = "https://conf.researchr.org/program/xp-2025/program-xp-2025/"
        html_content = self.scraper.get_page_content(program_url)
        if not html_content:
            logger.error("Failed to fetch program page")
            return
            
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Find all sessions with files
        all_sessions = self.scraper.find_all_session_links(soup)
        sessions_with_files = [s for s in all_sessions if s['has_files']]
        
        logger.info(f"Found {len(sessions_with_files)} sessions with files")
        
        # Group sessions by blocks
        session_blocks = self.scraper.group_sessions_by_blocks(all_sessions)
        
        # Create mapping of old files to new locations
        file_mappings = []
        
        for block_key, sessions_in_block in session_blocks.items():
            # Check if this block has any files
            has_files_in_block = any(s['session_data']['has_files'] for s in sessions_in_block)
            
            if not has_files_in_block:
                continue
                
            # Use the first session to determine folder structure
            first_session = sessions_in_block[0]
            link = first_session['session_data']['link_element']
            session_info = self.scraper.extract_session_info(link)
            
            # Calculate time range for the session block
            time_range = self.scraper.calculate_session_time_range(sessions_in_block)
            session_info['time_block'] = time_range
            
            # Create new folder structure
            new_folder_path = self.scraper.create_folder_structure(session_info, self.new_downloads_dir)
            
            logger.info(f"Processing session block: {block_key}")
            logger.info(f"  New folder: {new_folder_path}")
            
            # Find files from all sessions in this block
            for session in sessions_in_block:
                session_data = session['session_data']
                if not session_data['has_files']:
                    continue
                    
                link = session_data['link_element']
                individual_session_info = self.scraper.extract_session_info(link)
                
                # Create old folder path (individual session structure)
                old_folder_path = self.create_old_folder_structure(individual_session_info)
                
                if os.path.exists(old_folder_path):
                    logger.info(f"    Found old folder: {old_folder_path}")
                    
                    # Copy all files from old folder to new folder
                    for file_name in os.listdir(old_folder_path):
                        old_file_path = os.path.join(old_folder_path, file_name)
                        new_file_path = os.path.join(new_folder_path, file_name)
                        
                        if os.path.isfile(old_file_path):
                            # Copy file to new location
                            shutil.copy2(old_file_path, new_file_path)
                            logger.info(f"      Copied: {file_name}")
                            
                            file_mappings.append({
                                'old_path': old_file_path,
                                'new_path': new_file_path,
                                'session_title': individual_session_info.get('title', 'Unknown'),
                                'session_block': session_info.get('session_name', 'Unknown Block')
                            })
        
        logger.info(f"Reorganization completed. Moved {len(file_mappings)} files.")
        
        # Generate summary report
        self.generate_reorganization_report(file_mappings)
        
    def create_old_folder_structure(self, session_info):
        """Create the old individual session folder structure"""
        if not session_info:
            return self.old_downloads_dir
            
        # Clean up the date format
        date = session_info.get('date', 'Unknown_Date')
        date = self.scraper.sanitize_filename(date)
        
        # Clean up time block
        time_block = session_info.get('time_block', 'Unknown_Time')
        time_block = self.scraper.sanitize_filename(time_block.replace(':', '_').replace(' ', '_'))
        
        # Create conference name from title and track (old structure)
        title = session_info.get('title', 'Unknown_Session')
        track = session_info.get('track', '')
        
        if track:
            conference_name = f"{title} ({track})"
        else:
            conference_name = title
            
        conference_name = self.scraper.sanitize_filename(conference_name)
        
        # Create full path
        folder_path = os.path.join(self.old_downloads_dir, date, time_block, conference_name)
        
        return folder_path
    
    def generate_reorganization_report(self, file_mappings):
        """Generate a report of the reorganization"""
        report_file = "reorganization_report.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("XP 2025 Conference Downloads Reorganization Report\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"Total files reorganized: {len(file_mappings)}\n\n")
            
            # Group by session blocks
            blocks = {}
            for mapping in file_mappings:
                block = mapping['session_block']
                if block not in blocks:
                    blocks[block] = []
                blocks[block].append(mapping)
            
            f.write(f"Session blocks created: {len(blocks)}\n\n")
            
            for block_name, files in blocks.items():
                f.write(f"📁 {block_name}\n")
                f.write(f"   Files: {len(files)}\n")
                for file_mapping in files:
                    f.write(f"   - {os.path.basename(file_mapping['new_path'])}\n")
                    f.write(f"     From: {file_mapping['session_title'][:50]}...\n")
                f.write("\n")
        
        logger.info(f"Reorganization report saved: {report_file}")

def main():
    """Main function to reorganize downloads"""
    reorganizer = DownloadReorganizer()
    reorganizer.reorganize_downloads()

if __name__ == "__main__":
    main()
