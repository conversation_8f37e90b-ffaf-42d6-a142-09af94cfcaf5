#!/usr/bin/env python3
"""
Test script to verify session block downloads with the new folder structure
"""

import os
import shutil
from conference_scraper import ConferenceScraper
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_session_block_downloads():
    """Test downloading files with the new session block structure"""
    
    # Create a test downloads directory
    test_dir = "test_session_downloads"
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
    
    # Create a modified scraper that only processes a few sessions
    class TestConferenceScraper(ConferenceScraper):
        def __init__(self):
            super().__init__(excel_file="test_session_blocks.xlsx")
            
        def scrape_conference_files(self, program_url):
            """Modified version that only processes first few session blocks"""
            logger.info(f"Starting test scrape: {program_url}")
            
            # Get the main program page
            html_content = self.get_page_content(program_url)
            if not html_content:
                logger.error("Failed to fetch program page")
                return
                
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Find ALL session links (with and without files)
            all_sessions = self.find_all_session_links(soup)
            
            logger.info(f"Found {len(all_sessions)} total sessions")
            
            # Group sessions by session blocks
            session_blocks = self.group_sessions_by_blocks(all_sessions)
            
            logger.info(f"Grouped into {len(session_blocks)} session blocks")
            
            # Only process first 3 session blocks for testing
            block_count = 0
            total_downloads = 0
            
            for block_key, sessions_in_block in session_blocks.items():
                if block_count >= 3:  # Limit to first 3 blocks
                    break
                    
                try:
                    logger.info(f"Processing session block: {block_key}")
                    
                    # Use the first session to determine folder structure
                    first_session = sessions_in_block[0]
                    link = first_session['session_data']['link_element']
                    session_info = self.extract_session_info(link)
                    
                    # Create folder structure for this session block
                    folder_path = self.create_folder_structure(session_info, test_dir)
                    
                    # Process all sessions in this block
                    files_in_block = 0
                    
                    for session in sessions_in_block:
                        session_data = session['session_data']
                        link = session_data['link_element']
                        detail_url = session_data['detail_url']
                        has_files = session_data['has_files']
                        
                        # Extract session information
                        session_info = self.extract_session_info(link)
                        
                        # Convert relative URL to absolute if needed
                        from urllib.parse import urljoin
                        if detail_url.startswith('/'):
                            detail_url = urljoin(self.base_url, detail_url)
                        elif detail_url.startswith('#'):
                            detail_url = f"{program_url}{detail_url}"
                        
                        logger.info(f"  - {session_info.get('title', 'Unknown')} - Files: {has_files}")
                        
                        if has_files and not detail_url.startswith('#'):
                            # Get download information from detail page
                            downloads = self.get_download_info_from_detail_page(detail_url)
                            
                            if downloads:
                                # Download each file to the shared session block folder
                                for download_info in downloads:
                                    download_result = self.download_file(
                                        download_info['download_url'],
                                        download_info['filename'],
                                        folder_path
                                    )
                                    
                                    if download_result['success']:
                                        total_downloads += 1
                                        files_in_block += 1
                        
                        # Add session to Excel tracker
                        self.excel_tracker.add_session(session_info, detail_url, [])
                        
                        import time
                        time.sleep(0.3)
                    
                    logger.info(f"  Session block completed: {files_in_block} files downloaded to {folder_path}")
                    block_count += 1
                    
                except Exception as e:
                    logger.error(f"Error processing session block {block_key}: {e}")
                    continue
            
            logger.info(f"Test completed. Total files downloaded: {total_downloads}")
            
            # Generate Excel report
            logger.info("Generating Excel tracking report...")
            if self.excel_tracker.generate_excel_report():
                logger.info(f"Excel report saved: {self.excel_tracker.output_file}")
    
    # Run the test
    scraper = TestConferenceScraper()
    program_url = "https://conf.researchr.org/program/xp-2025/program-xp-2025/"
    
    scraper.scrape_conference_files(program_url)
    
    # Show the resulting folder structure
    logger.info("\n" + "="*50)
    logger.info("RESULTING FOLDER STRUCTURE:")
    logger.info("="*50)
    
    if os.path.exists(test_dir):
        for root, dirs, files in os.walk(test_dir):
            level = root.replace(test_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            logger.info(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                logger.info(f"{subindent}{file}")

if __name__ == "__main__":
    test_session_block_downloads()
